{"ast": null, "code": "var _jsxFileName = \"D:\\\\tmp\\\\image-to-react\\\\src\\\\App.js\";\nimport React from 'react';\nimport ScreenshotToCode from './components/ScreenshotToCode';\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"App\",\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"App-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"Screenshot to React Code Converter\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Upload any UI screenshot and get React component code instantly\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 10,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      children: /*#__PURE__*/_jsxDEV(ScreenshotToCode, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 13,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"footer\", {\n      className: \"App-footer\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Powered by AI - Convert any UI screenshot to React code\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 7,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "ScreenshotToCode", "jsxDEV", "_jsxDEV", "App", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/tmp/image-to-react/src/App.js"], "sourcesContent": ["import React from 'react';\nimport ScreenshotToCode from './components/ScreenshotToCode';\nimport './App.css';\n\nfunction App() {\n  return (\n    <div className=\"App\">\n      <header className=\"App-header\">\n        <h1>Screenshot to React Code Converter</h1>\n        <p>Upload any UI screenshot and get React component code instantly</p>\n      </header>\n      <main>\n        <ScreenshotToCode />\n      </main>\n      <footer className=\"App-footer\">\n        <p>Powered by AI - Convert any UI screenshot to React code</p>\n      </footer>\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,gBAAgB,MAAM,+BAA+B;AAC5D,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA;IAAKE,SAAS,EAAC,KAAK;IAAAC,QAAA,gBAClBH,OAAA;MAAQE,SAAS,EAAC,YAAY;MAAAC,QAAA,gBAC5BH,OAAA;QAAAG,QAAA,EAAI;MAAkC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC3CP,OAAA;QAAAG,QAAA,EAAG;MAA+D;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChE,CAAC,eACTP,OAAA;MAAAG,QAAA,eACEH,OAAA,CAACF,gBAAgB;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CAAC,eACPP,OAAA;MAAQE,SAAS,EAAC,YAAY;MAAAC,QAAA,eAC5BH,OAAA;QAAAG,QAAA,EAAG;MAAuD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV;AAACC,EAAA,GAfQP,GAAG;AAiBZ,eAAeA,GAAG;AAAC,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}