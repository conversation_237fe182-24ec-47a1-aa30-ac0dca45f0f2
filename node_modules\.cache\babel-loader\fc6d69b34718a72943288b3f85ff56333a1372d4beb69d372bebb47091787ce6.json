{"ast": null, "code": "'use strict';\n\nvar store = require('../internals/shared-store');\nmodule.exports = function (key, value) {\n  return store[key] || (store[key] = value || {});\n};", "map": {"version": 3, "names": ["store", "require", "module", "exports", "key", "value"], "sources": ["D:/tmp/image-to-react/node_modules/core-js-pure/internals/shared.js"], "sourcesContent": ["'use strict';\nvar store = require('../internals/shared-store');\n\nmodule.exports = function (key, value) {\n  return store[key] || (store[key] = value || {});\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,KAAK,GAAGC,OAAO,CAAC,2BAA2B,CAAC;AAEhDC,MAAM,CAACC,OAAO,GAAG,UAAUC,GAAG,EAAEC,KAAK,EAAE;EACrC,OAAOL,KAAK,CAACI,GAAG,CAAC,KAAKJ,KAAK,CAACI,GAAG,CAAC,GAAGC,KAAK,IAAI,CAAC,CAAC,CAAC;AACjD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}