{"ast": null, "code": "// This service will handle the API calls to the AI service\n\nconst API_KEY = process.env.REACT_APP_OPENAI_API_KEY; // You'll need to set this in your .env file\n\nexport const generateCodeFromImage = async imageFile => {\n  try {\n    // Convert image to base64\n    const base64Image = await fileToBase64(imageFile);\n\n    // For this example, we'll use OpenAI's GPT-4 Vision API\n    // You'll need to replace this with your actual API implementation\n    const response = await fetch('https://api.openai.com/v1/chat/completions', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'Authorization': `Bearer ${API_KEY}`\n      },\n      body: JSON.stringify({\n        model: \"gpt-4-vision-preview\",\n        messages: [{\n          role: \"user\",\n          content: [{\n            type: \"text\",\n            text: \"Generate React component code for this UI screenshot. Include appropriate CSS using Tailwind classes. Make the component responsive and follow best practices.\"\n          }, {\n            type: \"image_url\",\n            image_url: {\n              url: base64Image\n            }\n          }]\n        }],\n        max_tokens: 2000\n      })\n    });\n    const data = await response.json();\n    if (data.error) {\n      throw new Error(data.error.message);\n    }\n\n    // Extract the code from the response\n    const generatedText = data.choices[0].message.content;\n\n    // Extract code blocks from the response\n    const codeBlockRegex = /```(?:jsx|javascript|js|tsx|typescript|ts)?\\s*([\\s\\S]*?)```/g;\n    const matches = [...generatedText.matchAll(codeBlockRegex)];\n    if (matches.length > 0) {\n      return matches[0][1].trim();\n    }\n    return generatedText;\n  } catch (error) {\n    console.error('Error generating code:', error);\n    throw error;\n  }\n};\n\n// Helper function to convert file to base64\nconst fileToBase64 = file => {\n  return new Promise((resolve, reject) => {\n    const reader = new FileReader();\n    reader.readAsDataURL(file);\n    reader.onload = () => {\n      const base64String = reader.result;\n      resolve(base64String);\n    };\n    reader.onerror = error => {\n      reject(error);\n    };\n  });\n};", "map": {"version": 3, "names": ["API_KEY", "process", "env", "REACT_APP_OPENAI_API_KEY", "generateCodeFromImage", "imageFile", "base64Image", "fileToBase64", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "model", "messages", "role", "content", "type", "text", "image_url", "url", "max_tokens", "data", "json", "error", "Error", "message", "generatedText", "choices", "codeBlockRegex", "matches", "matchAll", "length", "trim", "console", "file", "Promise", "resolve", "reject", "reader", "FileReader", "readAsDataURL", "onload", "base64String", "result", "onerror"], "sources": ["D:/tmp/image-to-react/src/services/aiService.js"], "sourcesContent": ["// This service will handle the API calls to the AI service\n\nconst API_KEY = process.env.REACT_APP_OPENAI_API_KEY; // You'll need to set this in your .env file\n\nexport const generateCodeFromImage = async (imageFile) => {\n  try {\n    // Convert image to base64\n    const base64Image = await fileToBase64(imageFile);\n    \n    // For this example, we'll use OpenAI's GPT-4 Vision API\n    // You'll need to replace this with your actual API implementation\n    const response = await fetch('https://api.openai.com/v1/chat/completions', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'Authorization': `Bearer ${API_KEY}`\n      },\n      body: JSON.stringify({\n        model: \"gpt-4-vision-preview\",\n        messages: [\n          {\n            role: \"user\",\n            content: [\n              {\n                type: \"text\",\n                text: \"Generate React component code for this UI screenshot. Include appropriate CSS using Tailwind classes. Make the component responsive and follow best practices.\"\n              },\n              {\n                type: \"image_url\",\n                image_url: {\n                  url: base64Image\n                }\n              }\n            ]\n          }\n        ],\n        max_tokens: 2000\n      })\n    });\n\n    const data = await response.json();\n    \n    if (data.error) {\n      throw new Error(data.error.message);\n    }\n    \n    // Extract the code from the response\n    const generatedText = data.choices[0].message.content;\n    \n    // Extract code blocks from the response\n    const codeBlockRegex = /```(?:jsx|javascript|js|tsx|typescript|ts)?\\s*([\\s\\S]*?)```/g;\n    const matches = [...generatedText.matchAll(codeBlockRegex)];\n    \n    if (matches.length > 0) {\n      return matches[0][1].trim();\n    }\n    \n    return generatedText;\n  } catch (error) {\n    console.error('Error generating code:', error);\n    throw error;\n  }\n};\n\n// Helper function to convert file to base64\nconst fileToBase64 = (file) => {\n  return new Promise((resolve, reject) => {\n    const reader = new FileReader();\n    reader.readAsDataURL(file);\n    reader.onload = () => {\n      const base64String = reader.result;\n      resolve(base64String);\n    };\n    reader.onerror = (error) => {\n      reject(error);\n    };\n  });\n};\n"], "mappings": "AAAA;;AAEA,MAAMA,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,wBAAwB,CAAC,CAAC;;AAEtD,OAAO,MAAMC,qBAAqB,GAAG,MAAOC,SAAS,IAAK;EACxD,IAAI;IACF;IACA,MAAMC,WAAW,GAAG,MAAMC,YAAY,CAACF,SAAS,CAAC;;IAEjD;IACA;IACA,MAAMG,QAAQ,GAAG,MAAMC,KAAK,CAAC,4CAA4C,EAAE;MACzEC,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClC,eAAe,EAAE,UAAUX,OAAO;MACpC,CAAC;MACDY,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;QACnBC,KAAK,EAAE,sBAAsB;QAC7BC,QAAQ,EAAE,CACR;UACEC,IAAI,EAAE,MAAM;UACZC,OAAO,EAAE,CACP;YACEC,IAAI,EAAE,MAAM;YACZC,IAAI,EAAE;UACR,CAAC,EACD;YACED,IAAI,EAAE,WAAW;YACjBE,SAAS,EAAE;cACTC,GAAG,EAAEhB;YACP;UACF,CAAC;QAEL,CAAC,CACF;QACDiB,UAAU,EAAE;MACd,CAAC;IACH,CAAC,CAAC;IAEF,MAAMC,IAAI,GAAG,MAAMhB,QAAQ,CAACiB,IAAI,CAAC,CAAC;IAElC,IAAID,IAAI,CAACE,KAAK,EAAE;MACd,MAAM,IAAIC,KAAK,CAACH,IAAI,CAACE,KAAK,CAACE,OAAO,CAAC;IACrC;;IAEA;IACA,MAAMC,aAAa,GAAGL,IAAI,CAACM,OAAO,CAAC,CAAC,CAAC,CAACF,OAAO,CAACV,OAAO;;IAErD;IACA,MAAMa,cAAc,GAAG,8DAA8D;IACrF,MAAMC,OAAO,GAAG,CAAC,GAAGH,aAAa,CAACI,QAAQ,CAACF,cAAc,CAAC,CAAC;IAE3D,IAAIC,OAAO,CAACE,MAAM,GAAG,CAAC,EAAE;MACtB,OAAOF,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAACG,IAAI,CAAC,CAAC;IAC7B;IAEA,OAAON,aAAa;EACtB,CAAC,CAAC,OAAOH,KAAK,EAAE;IACdU,OAAO,CAACV,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAC9C,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,MAAMnB,YAAY,GAAI8B,IAAI,IAAK;EAC7B,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;IACtC,MAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;IAC/BD,MAAM,CAACE,aAAa,CAACN,IAAI,CAAC;IAC1BI,MAAM,CAACG,MAAM,GAAG,MAAM;MACpB,MAAMC,YAAY,GAAGJ,MAAM,CAACK,MAAM;MAClCP,OAAO,CAACM,YAAY,CAAC;IACvB,CAAC;IACDJ,MAAM,CAACM,OAAO,GAAIrB,KAAK,IAAK;MAC1Bc,MAAM,CAACd,KAAK,CAAC;IACf,CAAC;EACH,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}