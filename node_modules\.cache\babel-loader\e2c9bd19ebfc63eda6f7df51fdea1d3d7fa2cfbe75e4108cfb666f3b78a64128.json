{"ast": null, "code": "var _jsxFileName = \"D:\\\\tmp\\\\image-to-react\\\\src\\\\components\\\\ScreenshotToCode.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { generateCodeFromImage } from '../services/aiService';\nimport './ScreenshotToCode.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ScreenshotToCode = () => {\n  _s();\n  const [image, setImage] = useState(null);\n  const [preview, setPreview] = useState(null);\n  const [generatedCode, setGeneratedCode] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState('');\n  const handleImageUpload = e => {\n    const file = e.target.files[0];\n    if (file) {\n      setImage(file);\n      const reader = new FileReader();\n      reader.onloadend = () => {\n        setPreview(reader.result);\n      };\n      reader.readAsDataURL(file);\n      setError('');\n    }\n  };\n  const handleDrop = e => {\n    e.preventDefault();\n    const file = e.dataTransfer.files[0];\n    if (file && file.type.startsWith('image/')) {\n      setImage(file);\n      const reader = new FileReader();\n      reader.onloadend = () => {\n        setPreview(reader.result);\n      };\n      reader.readAsDataURL(file);\n      setError('');\n    } else {\n      setError('Please upload an image file');\n    }\n  };\n  const handleDragOver = e => {\n    e.preventDefault();\n  };\n  const generateCode = async () => {\n    if (!image) return;\n    setIsLoading(true);\n    setError('');\n    try {\n      const code = await generateCodeFromImage(image);\n      setGeneratedCode(code);\n    } catch (err) {\n      setError('Error generating code: ' + err.message);\n      console.error(err);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const resetForm = () => {\n    setImage(null);\n    setPreview(null);\n    setGeneratedCode('');\n    setError('');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"screenshot-to-code-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"Screenshot to React Code Converter\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"upload-area\",\n      onDrop: handleDrop,\n      onDragOver: handleDragOver,\n      children: preview ? /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          src: preview,\n          alt: \"Screenshot preview\",\n          className: \"preview-image\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"reset-button\",\n          onClick: resetForm,\n          children: \"Upload Different Image\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"upload-prompt\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Drag & drop a screenshot here or\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"file\",\n          accept: \"image/*\",\n          onChange: handleImageUpload,\n          id: \"file-upload\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"file-upload\",\n          className: \"upload-button\",\n          children: \"Select Image\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 73,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error-message\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 17\n    }, this), preview && /*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"generate-button\",\n      onClick: generateCode,\n      disabled: isLoading,\n      children: isLoading ? 'Generating Code...' : 'Generate React Code'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 104,\n      columnNumber: 9\n    }, this), isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading-indicator\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"spinner\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Analyzing image and generating code...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"loading-note\",\n        children: \"This may take up to 30 seconds depending on the complexity of the UI\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 9\n    }, this), generatedCode && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"code-output\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Generated React Code\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"pre\", {\n        children: generatedCode\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"button-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"copy-button\",\n          onClick: () => {\n            navigator.clipboard.writeText(generatedCode);\n            alert('Code copied to clipboard!');\n          },\n          children: \"Copy to Clipboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"download-button\",\n          onClick: () => {\n            const blob = new Blob([generatedCode], {\n              type: 'text/plain'\n            });\n            const url = URL.createObjectURL(blob);\n            const a = document.createElement('a');\n            a.href = url;\n            a.download = 'GeneratedComponent.jsx';\n            document.body.appendChild(a);\n            a.click();\n            document.body.removeChild(a);\n            URL.revokeObjectURL(url);\n          },\n          children: \"Download as .jsx\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 122,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 70,\n    columnNumber: 5\n  }, this);\n};\n_s(ScreenshotToCode, \"BIPrWj6qgBYhpcwXySDGWmklLlc=\");\n_c = ScreenshotToCode;\nexport default ScreenshotToCode;\nvar _c;\n$RefreshReg$(_c, \"ScreenshotToCode\");", "map": {"version": 3, "names": ["React", "useState", "generateCodeFromImage", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ScreenshotToCode", "_s", "image", "setImage", "preview", "setPreview", "generatedCode", "setGeneratedCode", "isLoading", "setIsLoading", "error", "setError", "handleImageUpload", "e", "file", "target", "files", "reader", "FileReader", "onloadend", "result", "readAsDataURL", "handleDrop", "preventDefault", "dataTransfer", "type", "startsWith", "handleDragOver", "generateCode", "code", "err", "message", "console", "resetForm", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onDrop", "onDragOver", "src", "alt", "onClick", "accept", "onChange", "id", "htmlFor", "disabled", "navigator", "clipboard", "writeText", "alert", "blob", "Blob", "url", "URL", "createObjectURL", "a", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "_c", "$RefreshReg$"], "sources": ["D:/tmp/image-to-react/src/components/ScreenshotToCode.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { generateCodeFromImage } from '../services/aiService';\nimport './ScreenshotToCode.css';\n\nconst ScreenshotToCode = () => {\n  const [image, setImage] = useState(null);\n  const [preview, setPreview] = useState(null);\n  const [generatedCode, setGeneratedCode] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState('');\n\n  const handleImageUpload = (e) => {\n    const file = e.target.files[0];\n    if (file) {\n      setImage(file);\n      const reader = new FileReader();\n      reader.onloadend = () => {\n        setPreview(reader.result);\n      };\n      reader.readAsDataURL(file);\n      setError('');\n    }\n  };\n\n  const handleDrop = (e) => {\n    e.preventDefault();\n    const file = e.dataTransfer.files[0];\n    if (file && file.type.startsWith('image/')) {\n      setImage(file);\n      const reader = new FileReader();\n      reader.onloadend = () => {\n        setPreview(reader.result);\n      };\n      reader.readAsDataURL(file);\n      setError('');\n    } else {\n      setError('Please upload an image file');\n    }\n  };\n\n  const handleDragOver = (e) => {\n    e.preventDefault();\n  };\n\n  const generateCode = async () => {\n    if (!image) return;\n    \n    setIsLoading(true);\n    setError('');\n    \n    try {\n      const code = await generateCodeFromImage(image);\n      setGeneratedCode(code);\n    } catch (err) {\n      setError('Error generating code: ' + err.message);\n      console.error(err);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const resetForm = () => {\n    setImage(null);\n    setPreview(null);\n    setGeneratedCode('');\n    setError('');\n  };\n\n  return (\n    <div className=\"screenshot-to-code-container\">\n      <h2>Screenshot to React Code Converter</h2>\n      \n      <div \n        className=\"upload-area\"\n        onDrop={handleDrop}\n        onDragOver={handleDragOver}\n      >\n        {preview ? (\n          <>\n            <img src={preview} alt=\"Screenshot preview\" className=\"preview-image\" />\n            <button className=\"reset-button\" onClick={resetForm}>\n              Upload Different Image\n            </button>\n          </>\n        ) : (\n          <div className=\"upload-prompt\">\n            <p>Drag & drop a screenshot here or</p>\n            <input \n              type=\"file\" \n              accept=\"image/*\" \n              onChange={handleImageUpload} \n              id=\"file-upload\"\n            />\n            <label htmlFor=\"file-upload\" className=\"upload-button\">\n              Select Image\n            </label>\n          </div>\n        )}\n      </div>\n      \n      {error && <div className=\"error-message\">{error}</div>}\n      \n      {preview && (\n        <button \n          className=\"generate-button\" \n          onClick={generateCode}\n          disabled={isLoading}\n        >\n          {isLoading ? 'Generating Code...' : 'Generate React Code'}\n        </button>\n      )}\n      \n      {isLoading && (\n        <div className=\"loading-indicator\">\n          <div className=\"spinner\"></div>\n          <p>Analyzing image and generating code...</p>\n          <p className=\"loading-note\">This may take up to 30 seconds depending on the complexity of the UI</p>\n        </div>\n      )}\n      \n      {generatedCode && (\n        <div className=\"code-output\">\n          <h3>Generated React Code</h3>\n          <pre>{generatedCode}</pre>\n          <div className=\"button-group\">\n            <button \n              className=\"copy-button\"\n              onClick={() => {\n                navigator.clipboard.writeText(generatedCode);\n                alert('Code copied to clipboard!');\n              }}\n            >\n              Copy to Clipboard\n            </button>\n            <button \n              className=\"download-button\"\n              onClick={() => {\n                const blob = new Blob([generatedCode], { type: 'text/plain' });\n                const url = URL.createObjectURL(blob);\n                const a = document.createElement('a');\n                a.href = url;\n                a.download = 'GeneratedComponent.jsx';\n                document.body.appendChild(a);\n                a.click();\n                document.body.removeChild(a);\n                URL.revokeObjectURL(url);\n              }}\n            >\n              Download as .jsx\n            </button>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default ScreenshotToCode;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,qBAAqB,QAAQ,uBAAuB;AAC7D,OAAO,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEhC,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGT,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACU,OAAO,EAAEC,UAAU,CAAC,GAAGX,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACY,aAAa,EAAEC,gBAAgB,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACc,SAAS,EAAEC,YAAY,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACgB,KAAK,EAAEC,QAAQ,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAEtC,MAAMkB,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAMC,IAAI,GAAGD,CAAC,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAIF,IAAI,EAAE;MACRX,QAAQ,CAACW,IAAI,CAAC;MACd,MAAMG,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,SAAS,GAAG,MAAM;QACvBd,UAAU,CAACY,MAAM,CAACG,MAAM,CAAC;MAC3B,CAAC;MACDH,MAAM,CAACI,aAAa,CAACP,IAAI,CAAC;MAC1BH,QAAQ,CAAC,EAAE,CAAC;IACd;EACF,CAAC;EAED,MAAMW,UAAU,GAAIT,CAAC,IAAK;IACxBA,CAAC,CAACU,cAAc,CAAC,CAAC;IAClB,MAAMT,IAAI,GAAGD,CAAC,CAACW,YAAY,CAACR,KAAK,CAAC,CAAC,CAAC;IACpC,IAAIF,IAAI,IAAIA,IAAI,CAACW,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE;MAC1CvB,QAAQ,CAACW,IAAI,CAAC;MACd,MAAMG,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,SAAS,GAAG,MAAM;QACvBd,UAAU,CAACY,MAAM,CAACG,MAAM,CAAC;MAC3B,CAAC;MACDH,MAAM,CAACI,aAAa,CAACP,IAAI,CAAC;MAC1BH,QAAQ,CAAC,EAAE,CAAC;IACd,CAAC,MAAM;MACLA,QAAQ,CAAC,6BAA6B,CAAC;IACzC;EACF,CAAC;EAED,MAAMgB,cAAc,GAAId,CAAC,IAAK;IAC5BA,CAAC,CAACU,cAAc,CAAC,CAAC;EACpB,CAAC;EAED,MAAMK,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI,CAAC1B,KAAK,EAAE;IAEZO,YAAY,CAAC,IAAI,CAAC;IAClBE,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF,MAAMkB,IAAI,GAAG,MAAMlC,qBAAqB,CAACO,KAAK,CAAC;MAC/CK,gBAAgB,CAACsB,IAAI,CAAC;IACxB,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZnB,QAAQ,CAAC,yBAAyB,GAAGmB,GAAG,CAACC,OAAO,CAAC;MACjDC,OAAO,CAACtB,KAAK,CAACoB,GAAG,CAAC;IACpB,CAAC,SAAS;MACRrB,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMwB,SAAS,GAAGA,CAAA,KAAM;IACtB9B,QAAQ,CAAC,IAAI,CAAC;IACdE,UAAU,CAAC,IAAI,CAAC;IAChBE,gBAAgB,CAAC,EAAE,CAAC;IACpBI,QAAQ,CAAC,EAAE,CAAC;EACd,CAAC;EAED,oBACEd,OAAA;IAAKqC,SAAS,EAAC,8BAA8B;IAAAC,QAAA,gBAC3CtC,OAAA;MAAAsC,QAAA,EAAI;IAAkC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAE3C1C,OAAA;MACEqC,SAAS,EAAC,aAAa;MACvBM,MAAM,EAAElB,UAAW;MACnBmB,UAAU,EAAEd,cAAe;MAAAQ,QAAA,EAE1B/B,OAAO,gBACNP,OAAA,CAAAE,SAAA;QAAAoC,QAAA,gBACEtC,OAAA;UAAK6C,GAAG,EAAEtC,OAAQ;UAACuC,GAAG,EAAC,oBAAoB;UAACT,SAAS,EAAC;QAAe;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACxE1C,OAAA;UAAQqC,SAAS,EAAC,cAAc;UAACU,OAAO,EAAEX,SAAU;UAAAE,QAAA,EAAC;QAErD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA,eACT,CAAC,gBAEH1C,OAAA;QAAKqC,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BtC,OAAA;UAAAsC,QAAA,EAAG;QAAgC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACvC1C,OAAA;UACE4B,IAAI,EAAC,MAAM;UACXoB,MAAM,EAAC,SAAS;UAChBC,QAAQ,EAAElC,iBAAkB;UAC5BmC,EAAE,EAAC;QAAa;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,eACF1C,OAAA;UAAOmD,OAAO,EAAC,aAAa;UAACd,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAEvD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAEL7B,KAAK,iBAAIb,OAAA;MAAKqC,SAAS,EAAC,eAAe;MAAAC,QAAA,EAAEzB;IAAK;MAAA0B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,EAErDnC,OAAO,iBACNP,OAAA;MACEqC,SAAS,EAAC,iBAAiB;MAC3BU,OAAO,EAAEhB,YAAa;MACtBqB,QAAQ,EAAEzC,SAAU;MAAA2B,QAAA,EAEnB3B,SAAS,GAAG,oBAAoB,GAAG;IAAqB;MAAA4B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnD,CACT,EAEA/B,SAAS,iBACRX,OAAA;MAAKqC,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChCtC,OAAA;QAAKqC,SAAS,EAAC;MAAS;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC/B1C,OAAA;QAAAsC,QAAA,EAAG;MAAsC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAC7C1C,OAAA;QAAGqC,SAAS,EAAC,cAAc;QAAAC,QAAA,EAAC;MAAoE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjG,CACN,EAEAjC,aAAa,iBACZT,OAAA;MAAKqC,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BtC,OAAA;QAAAsC,QAAA,EAAI;MAAoB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC7B1C,OAAA;QAAAsC,QAAA,EAAM7B;MAAa;QAAA8B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC1B1C,OAAA;QAAKqC,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BtC,OAAA;UACEqC,SAAS,EAAC,aAAa;UACvBU,OAAO,EAAEA,CAAA,KAAM;YACbM,SAAS,CAACC,SAAS,CAACC,SAAS,CAAC9C,aAAa,CAAC;YAC5C+C,KAAK,CAAC,2BAA2B,CAAC;UACpC,CAAE;UAAAlB,QAAA,EACH;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT1C,OAAA;UACEqC,SAAS,EAAC,iBAAiB;UAC3BU,OAAO,EAAEA,CAAA,KAAM;YACb,MAAMU,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACjD,aAAa,CAAC,EAAE;cAAEmB,IAAI,EAAE;YAAa,CAAC,CAAC;YAC9D,MAAM+B,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;YACrC,MAAMK,CAAC,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;YACrCF,CAAC,CAACG,IAAI,GAAGN,GAAG;YACZG,CAAC,CAACI,QAAQ,GAAG,wBAAwB;YACrCH,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,CAAC,CAAC;YAC5BA,CAAC,CAACO,KAAK,CAAC,CAAC;YACTN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,CAAC,CAAC;YAC5BF,GAAG,CAACW,eAAe,CAACZ,GAAG,CAAC;UAC1B,CAAE;UAAArB,QAAA,EACH;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACtC,EAAA,CAvJID,gBAAgB;AAAAqE,EAAA,GAAhBrE,gBAAgB;AAyJtB,eAAeA,gBAAgB;AAAC,IAAAqE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}